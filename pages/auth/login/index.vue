<template>
  <div class="auth_smcard_wrapper">
    <div class="form_wrapper login">
      <div class="box_title">
        <h2>{{ $t('admin.signin_title') }}</h2>
        <p>{{ $t('admin.signin_desc') }}</p>
      </div>
      <!-- end::box_title -->

      <client-only>
        <ValidationObserver ref="form">
          <b-form @submit.prevent="handleForm">
            <ValidationProvider rules="required|email" v-slot="{ errors }">
              <b-form-group>
                <label class="control-label" :class="{ invalid: errors[0] }">
                  {{ $t('admin.register.email') }}
                  <span class="star">*</span>
                </label>
                <b-input-group class="has-icon">
                  <template #prepend>
                    <svg class="icon" :class="{ invalid: errors[0] }">
                      <use
                        xlink:href="~/static/icons/regular.svg#envelope"
                      ></use>
                    </svg>
                  </template>
                  <b-form-input
                    type="text"
                    v-model="form.email"
                    :class="{ invalid: errors[0] }"
                    :placeholder="
                      $t('admin.enter_your') + ' ' + $t('admin.register.email')
                    "
                  ></b-form-input>
                </b-input-group>
                <span v-if="errors[0]" class="validation-error">
                  {{ errors[0] }}
                </span>
              </b-form-group>
            </ValidationProvider>
            <!-- end::form-group -->

            <ValidationProvider
              :rules="{
                required: true,
                min: 8,
              }"
              v-slot="{ errors }"
            >
              <b-form-group>
                <label class="control-label" :class="{ invalid: errors[0] }">
                  {{ $t('admin.register.password') }}
                  <span class="star">*</span>
                </label>
                <b-input-group class="has-icon">
                  <template #prepend>
                    <svg class="icon" :class="{ invalid: errors[0] }">
                      <use xlink:href="~/static/icons/regular.svg#lock"></use>
                    </svg>
                  </template>
                  <b-form-input
                    :type="showPassword ? 'text' : 'password'"
                    v-model="form.password"
                    :class="{ invalid: errors[0] }"
                    :placeholder="
                      $t('admin.enter_your') +
                      ' ' +
                      $t('admin.register.password')
                    "
                  ></b-form-input>
                  <template #append>
                    <button
                      type="button"
                      class="btn btn-outline-secondary password-toggle"
                      @click="togglePasswordVisibility"
                    >
                      <svg class="icon">
                        <use
                          :xlink:href="
                            showPassword
                              ? '~/static/icons/regular.svg#eye-slash'
                              : '~/static/icons/regular.svg#eye'
                          "
                        ></use>
                      </svg>
                    </button>
                  </template>
                </b-input-group>
                <span v-if="errors[0]" class="validation-error">
                  {{ errors[0] }}
                </span>
              </b-form-group>
            </ValidationProvider>
            <!-- end::form-group -->

            <b-form-group class="remember_wrapper">
              <nuxt-link :to="localePath({ name: 'auth-forget-password' })">
                {{ $t('admin.forget_password') }}
              </nuxt-link>
            </b-form-group>
            <!-- end::form-group -->

            <b-form-group class="submit_wrapper">
              <button
                type="submit"
                class="btn btn-default"
                :disabled="disabled"
              >
                <b-spinner variant="light" small v-if="disabled"></b-spinner>
                <span>{{ $t('admin.signin_title') }}</span>
              </button>
              <button type="button" class="btn" @click="handleSignIn">
                <img src="~/static/google.svg" alt="icon" />
                <span>{{ $t('admin.signup_with_google') }}</span>
              </button>
            </b-form-group>
            <!-- end::form-group -->

            <b-form-group class="register_toggle">
              <p>
                <span> {{ $t('admin.havenot_account') }} </span>
                <nuxt-link :to="localePath({ name: 'auth-register' })">
                  {{ $t('admin.signup_button') }}
                </nuxt-link>
              </p>
            </b-form-group>
            <!-- end::form-group -->
          </b-form>
        </ValidationObserver>
      </client-only>
    </div>
  </div>
  <!-- end::auth_smcard_wrapper -->
</template>

<script src="~/pages/auth/login/-script.js"></script>

<style lang="scss">
@import '~/pages/auth/login/-style.scss';
</style>

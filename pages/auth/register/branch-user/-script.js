// importing vuex tools
import { mapGetters } from 'vuex'

export default {
  name: 'BranchUserRegister',
  layout: 'ExternalForm',
  data() {
    return {
      form: {
        name: null,
        email: null,
        phone: null,
        password: null,
        image: null,
        code: null,
      },
      password_confirmation: null,
      image: null,
      disabled: false,
      showPassword: false,
      showConfirmPassword: false,
    }
  },
  mounted() {
    if (this.$route.query.email) {
      this.form.email = this.$route.query.email
    }
    if (this.$route.query.code) {
      this.form.code = this.$route.query.code
    }
  },
  methods: {
    uploadFiles($event) {
      const file = $event.target.files[0]
      const imageExt = ['png', 'jpg', 'jpeg', 'pdf', 'docx']
      const extension = $event.target.files[0].name
        .split('.')
        .pop()
        .toLowerCase()

      if (imageExt.includes(extension)) {
        this.form.image = file
        this.image = {
          name: file.name,
          media: URL.createObjectURL(file),
        }
      } else {
        this.TriggerNotify('error', this.$t('admin.extension_error'))
      }
    },
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true
      const form_data = new FormData()
      Object.entries(this.form).forEach((entry) => {
        if (entry[1] != null) {
          form_data.append(entry[0], entry[1])
        }
      })
      await this.$axios
        .post('/v2/client/auth/register-branch-user', form_data)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            this.$router.push(
              this.localePath({
                name: 'auth-login',
                query: { email: this.form.email },
              })
            )
          } else {
            this.TriggerNotify(this.notify.type, this.notify.message)
            this.disabled = false
          }
        })

      this.disabled = false
    },
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword
    },
    toggleConfirmPasswordVisibility() {
      this.showConfirmPassword = !this.showConfirmPassword
    },
  },
}
